import tops from '@/utils/tops';
import { commonTransforms, applyTransformSchema } from '@/utils/responseTransformers.js';

/**
 * Composable for working with system tags
 * @param {Object} options - Configuration options
 * @returns {Object} - Related methods and properties
 */
export default function useSystemTag (options) {
  const getAvailableSystemTags = async () => {
    try {
      // const { data: { Data } } = await tops.TOPSCompany.GetSystemDataTags();
      const response = await tops.TOPSCompany.GetSystemDataTags();
      const transformedData = applyTransformSchema(response.data, commonTransforms.systemTags);
      return transformedData.Data;
    } catch {
      return [];
    }
  };

  return {
    getAvailableSystemTags
  };
}
