<template>
  <app-grid-form context="inline">
    <SystemTagItem
      v-for="(tag, index) in internalTags"
      :tag="tag"
      :index="index"
      :key="`${tag.key}-${index}`"
      :available-tags="availableSystemTags"
      :can-edit-system-tags="canEditSystemTags"
      @remove-tag="removeTag"
      @update-tag="updateTag" />

    <button class="_add" @click="addTag" :disabled="!canAddTag">
      <i class="fas fa-plus"></i> Add
    </button>

    <pre>internalTags: {{ internalTags }}</pre>
    <!-- <pre>availableSystemTags: {{ availableSystemTags }}</pre> -->

  </app-grid-form>
</template>

<script>
// v-model compatibility for Vue 2.7
export default {
  model: {
    prop: 'modelValue',
    event: 'update:modelValue'
  }
};
</script>

<script setup>
import { ref, computed, watch, getCurrentInstance } from 'vue';
import useSystemTag from './useSystemTag.js';
import Access from '@/utils/access.js';
import SystemTagItem from './SystemTagItem.vue';

const props = defineProps({
  modelValue: { type: String, default: '' },
  canEditSystemTags: { type: Boolean, default: false }
});
const emit = defineEmits(['update:modelValue']);

const internalTags = ref([]);
const availableSystemTags = ref([]);

const canAddTag = computed(() => {
  if (!props.canEditSystemTags) return false;

  const usedKeys = internalTags.value.map(tag => tag.key);
  const hasUnusedTags = availableSystemTags.value.some(tag => !usedKeys.includes(tag.ShortCode));

  return hasUnusedTags;
});

const parseSystemTags = (value = '') =>
  value.length >= 3
    ? value
      .split(';')
      .filter(Boolean)
      .map(pair => {
        const [key = '', ...rest] = pair.split('=');
        return { key, value: rest.join('=') };
      })
    : [];

const flattenSystemTags = tags =>
  tags
    .filter(({ key, value }) => key || value)
    .map(({ key = '', value = '' }) => `${key}=${value}`)
    .join(';');

watch(
  () => parseSystemTags(props.modelValue),
  tags => {
    internalTags.value = tags;
  },
  { immediate: true }
);

watch(
  internalTags,
  tags => {
    const flattened = flattenSystemTags(tags);
    if (flattened !== props.modelValue) {
      emit('update:modelValue', flattened);
    }
  },
  { deep: true }
);

const addTag = () => {
  // Find the first unused tag from available system tags
  const usedKeys = internalTags.value.map(tag => tag.key);
  const unusedTag = availableSystemTags.value.find(tag => !usedKeys.includes(tag.ShortCode));
  const defaultKey = unusedTag ? unusedTag.ShortCode : '';

  internalTags.value.push({
    key: defaultKey,
    value: '',
    isNew: true
  });
};
const removeTag = index => internalTags.value.splice(index, 1);
const updateTag = (index, tag) => internalTags.value.splice(index, 1, tag);

const systemTagUtil = useSystemTag();
const getAvailableSystemTags = async () => {
  availableSystemTags.value = await systemTagUtil.getAvailableSystemTags();
};
getAvailableSystemTags();
</script>

<style scoped>
._add {
  position: absolute;
  right: 0.5rlh;
  top: -1.7rlh;

  padding: 0.25rem 0.75rem;
  font-size: var(--font-size-small1);
  font-weight: bold;
  appearance: none;
  color: var(--white);
  background-color: color-mix(in oklch, var(--white), transparent 80%);
  border: 0;
  border-radius: 0.2rem;

  &:hover {
    background-color: color-mix(in oklch, var(--white), transparent 70%);
  }

  &:disabled {
    opacity: 0.5;
  }
}
</style>
